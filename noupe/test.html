<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #2196F3;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #2196F3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976D2;
        }
        
        button.secondary {
            background: #666;
        }
        
        button.secondary:hover {
            background: #555;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 Noupe Chatbot Test Suite</h1>
        
        <div class="instructions">
            <strong>Instructions:</strong> This page tests all chatbot functionality. Use the buttons below to test different features and configurations. The chatbot should appear in the bottom-right corner.
        </div>
        
        <div class="test-section">
            <h3>🚀 Basic Functionality Tests</h3>
            <div class="test-buttons">
                <button onclick="testBasicInit()">Initialize Chatbot</button>
                <button onclick="testOpen()">Open Chatbot</button>
                <button onclick="testClose()">Close Chatbot</button>
                <button onclick="testToggle()">Toggle Chatbot</button>
                <button onclick="testSendMessage()">Send Test Message</button>
            </div>
            <div id="basic-status" class="status info">Ready to test basic functionality</div>
        </div>
        
        <div class="test-section">
            <h3>🎨 Theme Tests</h3>
            <div class="test-buttons">
                <button onclick="testTheme('blue')">Blue Theme</button>
                <button onclick="testTheme('green')">Green Theme</button>
                <button onclick="testTheme('purple')">Purple Theme</button>
                <button onclick="testTheme('orange')">Orange Theme</button>
                <button onclick="testTheme('dark')">Dark Theme</button>
            </div>
            <div id="theme-status" class="status info">Click a theme to test</div>
        </div>
        
        <div class="test-section">
            <h3>📍 Position Tests</h3>
            <div class="test-buttons">
                <button onclick="testPosition('bottom-right')">Bottom Right</button>
                <button onclick="testPosition('bottom-left')">Bottom Left</button>
                <button onclick="testPosition('top-right')">Top Right</button>
                <button onclick="testPosition('top-left')">Top Left</button>
            </div>
            <div id="position-status" class="status info">Click a position to test</div>
        </div>
        
        <div class="test-section">
            <h3>📏 Size Tests</h3>
            <div class="test-buttons">
                <button onclick="testSize('small')">Small</button>
                <button onclick="testSize('medium')">Medium</button>
                <button onclick="testSize('large')">Large</button>
            </div>
            <div id="size-status" class="status info">Click a size to test</div>
        </div>
        
        <div class="test-section">
            <h3>💬 Response Tests</h3>
            <div class="test-buttons">
                <button onclick="testResponse('Hello')">Test Greeting</button>
                <button onclick="testResponse('What are your prices?')">Test Pricing</button>
                <button onclick="testResponse('How can I contact you?')">Test Contact</button>
                <button onclick="testResponse('What are your hours?')">Test Hours</button>
                <button onclick="testResponse('Tell me about your services')">Test Services</button>
            </div>
            <div id="response-status" class="status info">Click to test different responses</div>
        </div>
        
        <div class="test-section">
            <h3>⚙️ Configuration Tests</h3>
            <div class="test-buttons">
                <button onclick="testAutoOpen()">Test Auto-Open</button>
                <button onclick="testCustomGreeting()">Custom Greeting</button>
                <button onclick="testTypingIndicator()">Typing Indicator</button>
                <button onclick="testNotifications()">Notifications</button>
            </div>
            <div id="config-status" class="status info">Test various configuration options</div>
        </div>
        
        <div class="test-section">
            <h3>📱 Responsive Tests</h3>
            <div class="test-buttons">
                <button onclick="testMobile()">Simulate Mobile</button>
                <button onclick="testTablet()">Simulate Tablet</button>
                <button onclick="testDesktop()">Simulate Desktop</button>
            </div>
            <div id="responsive-status" class="status info">Test responsive behavior</div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Advanced Tests</h3>
            <div class="test-buttons">
                <button onclick="testConversationHistory()">Get History</button>
                <button onclick="testClearConversation()">Clear Conversation</button>
                <button onclick="testCustomConfig()">Custom Config</button>
                <button onclick="testErrorHandling()">Error Handling</button>
            </div>
            <div id="advanced-status" class="status info">Test advanced functionality</div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="test-results">
                <div class="code-snippet" id="results-output">Test results will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Chatbot Integration -->
    <link rel="stylesheet" href="chatbot/css/chatbot.css">
    <link rel="stylesheet" href="chatbot/css/themes.css">
    <script src="chatbot/js/config.js"></script>
    <script src="chatbot/js/responses.js"></script>
    <script src="chatbot/js/chatbot.js"></script>
    
    <script>
        let testResults = [];
        let currentChatbot = null;
        
        function updateStatus(sectionId, message, type = 'info') {
            const statusEl = document.getElementById(sectionId);
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
            }
        }
        
        function logResult(test, result, details = '') {
            testResults.push({
                test: test,
                result: result,
                details: details,
                timestamp: new Date().toISOString()
            });
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const output = document.getElementById('results-output');
            if (output) {
                output.textContent = JSON.stringify(testResults, null, 2);
            }
        }
        
        function reinitializeChatbot(config = {}) {
            // Remove existing chatbot
            const existing = document.getElementById('noupe-chatbot-container');
            if (existing) {
                existing.remove();
            }
            
            // Create new instance
            if (window.NoupeChatbot) {
                currentChatbot = new window.NoupeChatbot.constructor();
                currentChatbot.init(config);
                return true;
            }
            return false;
        }
        
        // Basic functionality tests
        function testBasicInit() {
            try {
                const success = reinitializeChatbot();
                if (success) {
                    updateStatus('basic-status', 'Chatbot initialized successfully!', 'success');
                    logResult('Basic Init', 'PASS', 'Chatbot initialized without errors');
                } else {
                    updateStatus('basic-status', 'Failed to initialize chatbot', 'error');
                    logResult('Basic Init', 'FAIL', 'NoupeChatbot not available');
                }
            } catch (error) {
                updateStatus('basic-status', `Error: ${error.message}`, 'error');
                logResult('Basic Init', 'FAIL', error.message);
            }
        }
        
        function testOpen() {
            if (currentChatbot) {
                currentChatbot.open();
                updateStatus('basic-status', 'Chatbot opened', 'success');
                logResult('Open', 'PASS', 'Chatbot opened programmatically');
            } else {
                updateStatus('basic-status', 'No chatbot instance available', 'error');
                logResult('Open', 'FAIL', 'No chatbot instance');
            }
        }
        
        function testClose() {
            if (currentChatbot) {
                currentChatbot.close();
                updateStatus('basic-status', 'Chatbot closed', 'success');
                logResult('Close', 'PASS', 'Chatbot closed programmatically');
            } else {
                updateStatus('basic-status', 'No chatbot instance available', 'error');
                logResult('Close', 'FAIL', 'No chatbot instance');
            }
        }
        
        function testToggle() {
            if (currentChatbot) {
                currentChatbot.toggle();
                updateStatus('basic-status', 'Chatbot toggled', 'success');
                logResult('Toggle', 'PASS', 'Chatbot toggled programmatically');
            } else {
                updateStatus('basic-status', 'No chatbot instance available', 'error');
                logResult('Toggle', 'FAIL', 'No chatbot instance');
            }
        }
        
        function testSendMessage() {
            if (currentChatbot && currentChatbot.sendMessage) {
                currentChatbot.sendMessage('This is a test message from the test suite!');
                updateStatus('basic-status', 'Test message sent', 'success');
                logResult('Send Message', 'PASS', 'Test message sent programmatically');
            } else {
                updateStatus('basic-status', 'Cannot send message', 'error');
                logResult('Send Message', 'FAIL', 'sendMessage method not available');
            }
        }
        
        // Theme tests
        function testTheme(theme) {
            try {
                const success = reinitializeChatbot({ theme: theme });
                if (success) {
                    updateStatus('theme-status', `${theme} theme applied successfully`, 'success');
                    logResult(`Theme: ${theme}`, 'PASS', `${theme} theme applied`);
                } else {
                    updateStatus('theme-status', `Failed to apply ${theme} theme`, 'error');
                    logResult(`Theme: ${theme}`, 'FAIL', 'Failed to reinitialize');
                }
            } catch (error) {
                updateStatus('theme-status', `Error applying ${theme} theme: ${error.message}`, 'error');
                logResult(`Theme: ${theme}`, 'FAIL', error.message);
            }
        }
        
        // Position tests
        function testPosition(position) {
            try {
                const success = reinitializeChatbot({ position: position });
                if (success) {
                    updateStatus('position-status', `Position ${position} applied successfully`, 'success');
                    logResult(`Position: ${position}`, 'PASS', `${position} position applied`);
                } else {
                    updateStatus('position-status', `Failed to apply ${position} position`, 'error');
                    logResult(`Position: ${position}`, 'FAIL', 'Failed to reinitialize');
                }
            } catch (error) {
                updateStatus('position-status', `Error applying ${position} position: ${error.message}`, 'error');
                logResult(`Position: ${position}`, 'FAIL', error.message);
            }
        }
        
        // Size tests
        function testSize(size) {
            try {
                const success = reinitializeChatbot({ size: size });
                if (success) {
                    updateStatus('size-status', `Size ${size} applied successfully`, 'success');
                    logResult(`Size: ${size}`, 'PASS', `${size} size applied`);
                } else {
                    updateStatus('size-status', `Failed to apply ${size} size`, 'error');
                    logResult(`Size: ${size}`, 'FAIL', 'Failed to reinitialize');
                }
            } catch (error) {
                updateStatus('size-status', `Error applying ${size} size: ${error.message}`, 'error');
                logResult(`Size: ${size}`, 'FAIL', error.message);
            }
        }
        
        // Response tests
        function testResponse(message) {
            if (currentChatbot) {
                currentChatbot.open();
                setTimeout(() => {
                    if (currentChatbot.sendMessage) {
                        currentChatbot.sendMessage(message);
                        updateStatus('response-status', `Sent: "${message}"`, 'success');
                        logResult(`Response Test: ${message}`, 'PASS', 'Message sent and response expected');
                    }
                }, 500);
            } else {
                updateStatus('response-status', 'No chatbot instance available', 'error');
                logResult(`Response Test: ${message}`, 'FAIL', 'No chatbot instance');
            }
        }
        
        // Configuration tests
        function testAutoOpen() {
            const success = reinitializeChatbot({ autoOpen: true, autoOpenDelay: 1000 });
            if (success) {
                updateStatus('config-status', 'Auto-open test started (1 second delay)', 'info');
                logResult('Auto Open', 'PASS', 'Auto-open configured with 1s delay');
            } else {
                updateStatus('config-status', 'Failed to test auto-open', 'error');
                logResult('Auto Open', 'FAIL', 'Failed to reinitialize');
            }
        }
        
        function testCustomGreeting() {
            const greeting = 'Welcome to our test environment! This is a custom greeting message.';
            const success = reinitializeChatbot({ greeting: greeting });
            if (success) {
                updateStatus('config-status', 'Custom greeting applied', 'success');
                logResult('Custom Greeting', 'PASS', 'Custom greeting message set');
            } else {
                updateStatus('config-status', 'Failed to apply custom greeting', 'error');
                logResult('Custom Greeting', 'FAIL', 'Failed to reinitialize');
            }
        }
        
        function testTypingIndicator() {
            const success = reinitializeChatbot({ showTypingIndicator: true, typingDelay: 2000 });
            if (success) {
                updateStatus('config-status', 'Typing indicator test configured', 'success');
                logResult('Typing Indicator', 'PASS', 'Typing indicator enabled with 2s delay');
            } else {
                updateStatus('config-status', 'Failed to configure typing indicator', 'error');
                logResult('Typing Indicator', 'FAIL', 'Failed to reinitialize');
            }
        }
        
        function testNotifications() {
            if (currentChatbot && currentChatbot.showNotification) {
                currentChatbot.showNotification();
                updateStatus('config-status', 'Notification test triggered', 'success');
                logResult('Notifications', 'PASS', 'Notification badge shown');
            } else {
                updateStatus('config-status', 'Notification method not available', 'error');
                logResult('Notifications', 'FAIL', 'showNotification method not found');
            }
        }
        
        // Responsive tests
        function testMobile() {
            document.body.style.width = '375px';
            updateStatus('responsive-status', 'Mobile viewport simulated (375px)', 'info');
            logResult('Mobile Responsive', 'PASS', 'Viewport set to mobile size');
        }
        
        function testTablet() {
            document.body.style.width = '768px';
            updateStatus('responsive-status', 'Tablet viewport simulated (768px)', 'info');
            logResult('Tablet Responsive', 'PASS', 'Viewport set to tablet size');
        }
        
        function testDesktop() {
            document.body.style.width = 'auto';
            updateStatus('responsive-status', 'Desktop viewport restored', 'info');
            logResult('Desktop Responsive', 'PASS', 'Viewport restored to desktop');
        }
        
        // Advanced tests
        function testConversationHistory() {
            if (currentChatbot && currentChatbot.getConversationHistory) {
                const history = currentChatbot.getConversationHistory();
                updateStatus('advanced-status', `History retrieved: ${history.length} messages`, 'success');
                logResult('Conversation History', 'PASS', `${history.length} messages in history`);
            } else {
                updateStatus('advanced-status', 'History method not available', 'error');
                logResult('Conversation History', 'FAIL', 'getConversationHistory method not found');
            }
        }
        
        function testClearConversation() {
            if (currentChatbot && currentChatbot.clearConversation) {
                currentChatbot.clearConversation();
                updateStatus('advanced-status', 'Conversation cleared', 'success');
                logResult('Clear Conversation', 'PASS', 'Conversation history cleared');
            } else {
                updateStatus('advanced-status', 'Clear method not available', 'error');
                logResult('Clear Conversation', 'FAIL', 'clearConversation method not found');
            }
        }
        
        function testCustomConfig() {
            const customConfig = {
                theme: 'purple',
                position: 'top-left',
                size: 'large',
                greeting: 'Custom configuration test!',
                widgetTitle: 'Test Bot',
                widgetSubtitle: 'Custom Config Active',
                autoOpen: false,
                showTypingIndicator: true,
                enableEmojis: true,
                maxMessages: 50
            };
            
            const success = reinitializeChatbot(customConfig);
            if (success) {
                updateStatus('advanced-status', 'Custom configuration applied', 'success');
                logResult('Custom Config', 'PASS', 'Complex configuration applied successfully');
            } else {
                updateStatus('advanced-status', 'Failed to apply custom config', 'error');
                logResult('Custom Config', 'FAIL', 'Failed to apply configuration');
            }
        }
        
        function testErrorHandling() {
            try {
                // Test with invalid configuration
                reinitializeChatbot({ theme: 'invalid-theme', position: 'invalid-position' });
                updateStatus('advanced-status', 'Error handling test completed', 'success');
                logResult('Error Handling', 'PASS', 'Invalid config handled gracefully');
            } catch (error) {
                updateStatus('advanced-status', 'Error handling test failed', 'error');
                logResult('Error Handling', 'FAIL', `Unhandled error: ${error.message}`);
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            testBasicInit();
        });
    </script>
</body>
</html>
