import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';

const AutomotiveGroupAnalyzer = () => {
  const [data, setData] = useState([]);
  const [analysis, setAnalysis] = useState(null);
  const [selectedView, setSelectedView] = useState('summary');

  // US Census Regional Definitions
  const regions = {
    'Northeast': ['CT', 'ME', 'MA', 'NH', 'NJ', 'NY', 'PA', 'RI', 'VT'],
    'Midwest': ['IL', 'IN', 'IA', 'KS', 'MI', 'MN', 'MO', 'NE', 'ND', 'OH', 'SD', 'WI'],
    'South': ['AL', 'AR', 'DE', 'FL', 'GA', 'KY', 'LA', '<PERSON>', 'MS', 'NC', 'OK', 'SC', 'TN', 'TX', 'VA', 'WV'],
    'West': ['AK', 'AZ', 'CA', 'CO', 'HI', 'ID', 'MT', 'NV', 'NM', 'OR', 'UT', 'WA', 'WY'],
    'International': ['ON'] // Canadian provinces
  };

  const rawData = `Serra Automotive|Serra Lexus Lansing|MI
Michael Comuzzi Auto Group|Superior Hyundai|ON
Passport Automotive Group|Passport BMW|MD
Walterboro Ford|Walterboro Ford|SC
Loyal Test AG|Love Chevrolet|SC
Ford of Dalton|Ford of Dalton|GA
Faith's Auto Group|Faith's Toyota Ford|VT
Colley Ford|Colley Ford|CA
Loyal Test AG|Love Chevrolet|SC
Passport Automotive Group|MINI of Alexandria|VA
East Dallas Volkswagen|East Dallas Volkswagen|TX
All Star Automotive Group|All Star Ford|LA
All Star Automotive Group|All Star Chevrolet|LA
All Star Automotive Group|All Star Chevrolet North|LA
All Star Automotive Group|All Star Dodge Chrysler Jeep Ram|LA
Passport Automotive Group|Passport MINI of Montgomery County|MD
Criswell Auto Group|Criswell CDJR of Woodstock|VA
Copeland Auto Group|Copeland Chevrolet Hyannis|MA
Grainger Auto|Grainger Nissan of Anderson|SC
Haley Automotive Group|Haley Chrysler Dodge Jeep Ram|VA
Keystone AG OK|Carter County Hyundai|OK
Bob Bell Automotive Group|Bob Bell Chevrolet of Bel Air|MD
Haley Automotive Group|Haley Buick GMC Truck|VA
Haley Automotive Group|Haley Buick GMC Airport|VA
Copeland Auto Group|Copeland Subaru Hyannis|MA
Anderson Automotive Group (NC)|Fred Anderson Kia of Greenville|SC
Sam Leman Automotive Group|Sam Leman_Mercedes of Champaign|IL
Sam Leman Automotive Group|Sam Leman Chevrolet Cadillac|IL
Haley Automotive Group|Haley Toyota of Roanoke|VA
Anderson Automotive Group (NC)|Fred Anderson Kia of Greer|SC
Harnish Auto Family|Kia of Everett|WA
Keystone AG OK|Carter County CDJR|OK
Whitten Brothers|Whitten Bros Chrysler Dodge Jeep of Richmond|VA
Robert Brogden AG|Robert Brogden Olathe Buick GMC|KS
Myrtle Beach Chevrolet|Myrtle Beach Chevrolet|SC
Haley Automotive Group|Haley Automall|VA
Sunrise Auto Group|Sunrise Toyota North|NY
Haley Automotive Group|Haley Toyota Certified Pre-Owned Center|VA
Whitten Brothers|Whitten Bros Mazda|VA
Haley Automotive Group|Volvo Cars Richmond|VA
Karl Auto Group|Karl Chevrolet of Ankeny|IA
Bob Bell Automotive Group|Bob Bell Hyundai|MD
Anderson Automotive Group (NC)|Fred Anderson Toyota of Greer|SC
Whitten Brothers|Whitten Brothers Chrysler Dodge Jeep RAM of Richmond|VA
Sunrise Auto Group|Sunrise Toyota|NY
Criswell Auto Group|Criswell Chevrolet|MD
Ourisman Automotive (Main)|Toyota of Woodbridge|VA
Safford Automotive Group|Safford Brown Honda Arlington|VA
Criswell Auto Group|Criswell Chrysler Jeep Dodge Ram Fiat|MD
Anderson Automotive Group (NC)|Fred Anderson Honda|SC
All Star Automotive Group|All Star Kia|LA
All Star Automotive Group|All Star Kia East|LA
All Star Automotive Group|All Star Hyundai|LA
Safford Automotive Group|Safford Brown CDJR Sterling|VA
Kenwood Dealer Group|Beechmont Subaru|OH
L & L Motor Co|L & L Ford - CDJR|UT
Safford Automotive Group|Safford Brown Honda Glen Burnie|MD
Hollingsworth Richards Ford|Hollingsworth Richards Ford|LA
Haley Automotive Group|Haley Toyota of Richmond-Midlothian|VA
Pat Milliken Ford|Pat Milliken Ford|MI
Page Auto Group VA|Audi Richmond|VA
Page Auto Group VA|West Broad Honda|VA
Billion Automotive|Sioux Falls Chrysler Jeep Dodge Ram Fiat|SD
Karl Auto Group|Karl Chevrolet of Stuart|IA
Safford Automotive Group|Safford Kia of Fredericksburg|VA
Goss Auto Group|Goss Dodge Chrysler Ram Jeep|VT
Hunter Auto Group|Hunter Volvo|NC
Findlay Automotive Group|Findlay Volvo Cars Las Vegas|NV
Bob Bell Automotive Group|Bob Bell Ford|MD
Raabe Ford Lincoln|Raabe Ford Lincoln|OH
Ed Kenley Ford|Ed Kenley Ford|UT
Young Automotive Group MI|Kia of Jackson|MI
RiverHead Motors|Riverhead GMC|NY
Burns Kull Automotive|Burns Honda|NJ
Don K Auto Group|Don K Chrysler Dodge Jeep Ram|MT
Billion Automotive|Iowa City Chevrolet Buick GMC|IA
Safford Automotive Group|Safford Brown Subaru Manassas|VA`;

  const getStateRegion = (stateCode) => {
    // Handle special case for test data
    if (stateCode === 'SC11') return 'South';
    
    for (const [region, states] of Object.entries(regions)) {
      if (states.includes(stateCode)) {
        return region;
      }
    }
    return 'Unknown';
  };

  const parseData = () => {
    const parsed = [];
    const lines = rawData.trim().split('\n');
    
    lines.forEach(line => {
      const parts = line.split('|');
      if (parts.length === 3) {
        const [groupName, storeName, stateCode] = parts;
        parsed.push({
          groupName: groupName.trim(),
          storeName: storeName.trim(),
          stateCode: stateCode.trim()
        });
      }
    });
    
    return parsed;
  };

  const analyzeData = (parsedData) => {
    const groupAnalysis = {};
    
    // Group stores by automotive group
    parsedData.forEach(store => {
      if (!groupAnalysis[store.groupName]) {
        groupAnalysis[store.groupName] = {
          stores: [],
          stateDistribution: {},
          regionDistribution: {}
        };
      }
      
      groupAnalysis[store.groupName].stores.push(store);
      
      // Count states
      const state = store.stateCode;
      groupAnalysis[store.groupName].stateDistribution[state] = 
        (groupAnalysis[store.groupName].stateDistribution[state] || 0) + 1;
      
      // Count regions
      const region = getStateRegion(state);
      groupAnalysis[store.groupName].regionDistribution[region] = 
        (groupAnalysis[store.groupName].regionDistribution[region] || 0) + 1;
    });

    // Determine primary region for each group
    const finalAssignments = {};
    Object.entries(groupAnalysis).forEach(([groupName, data]) => {
      const regionCounts = data.regionDistribution;
      let primaryRegion = '';
      let maxCount = 0;

      // Find region with most stores
      Object.entries(regionCounts).forEach(([region, count]) => {
        if (count > maxCount || (count === maxCount && region < primaryRegion)) {
          maxCount = count;
          primaryRegion = region;
        }
      });

      finalAssignments[groupName] = {
        primaryRegion,
        totalStores: data.stores.length,
        stateDistribution: data.stateDistribution,
        regionDistribution: data.regionDistribution,
        reasoning: generateReasoning(data, primaryRegion)
      };
    });

    return { groupAnalysis, finalAssignments };
  };

  const generateReasoning = (data, primaryRegion) => {
    const totalStores = data.stores.length;
    const regionCount = data.regionDistribution[primaryRegion];
    
    if (totalStores === 1) {
      return 'Single store location';
    } else if (regionCount === totalStores) {
      return `All ${totalStores} stores in ${primaryRegion}`;
    } else {
      return `${regionCount} of ${totalStores} stores in ${primaryRegion}`;
    }
  };

  useEffect(() => {
    const parsedData = parseData();
    setData(parsedData);
    const analysisResult = analyzeData(parsedData);
    setAnalysis(analysisResult);
  }, []);

  const getRegionSummary = () => {
    if (!analysis) return [];
    
    const regionCounts = {};
    Object.values(analysis.finalAssignments).forEach(assignment => {
      regionCounts[assignment.primaryRegion] = (regionCounts[assignment.primaryRegion] || 0) + 1;
    });

    return Object.entries(regionCounts).map(([region, count]) => ({
      region,
      count
    }));
  };

  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#ff0000'];

  if (!analysis) {
    return <div className="p-6">Loading analysis...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">
        Automotive Group Regional Analysis
      </h1>

      <div className="mb-6">
        <div className="flex space-x-4 mb-4">
          <button
            onClick={() => setSelectedView('summary')}
            className={`px-4 py-2 rounded ${
              selectedView === 'summary' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Regional Summary
          </button>
          <button
            onClick={() => setSelectedView('detailed')}
            className={`px-4 py-2 rounded ${
              selectedView === 'detailed' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Detailed Analysis
          </button>
          <button
            onClick={() => setSelectedView('methodology')}
            className={`px-4 py-2 rounded ${
              selectedView === 'methodology' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Methodology
          </button>
        </div>
      </div>

      {selectedView === 'summary' && (
        <div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Groups by Region</h2>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={getRegionSummary()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="region" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Regional Distribution</h2>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={getRegionSummary()}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ region, count }) => `${region}: ${count}`}
                  >
                    {getRegionSummary().map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Quick Stats</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Object.keys(analysis.finalAssignments).length}
                </div>
                <div className="text-sm text-gray-600">Total Groups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.length}
                </div>
                <div className="text-sm text-gray-600">Total Stores</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {getRegionSummary().length}
                </div>
                <div className="text-sm text-gray-600">Regions Covered</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {new Set(data.map(d => d.stateCode)).size}
                </div>
                <div className="text-sm text-gray-600">States/Provinces</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedView === 'detailed' && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Detailed Group Assignments</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-300">
              <thead className="bg-gray-100">
                <tr>
                  <th className="px-4 py-2 text-left">Group Name</th>
                  <th className="px-4 py-2 text-left">Assigned Region</th>
                  <th className="px-4 py-2 text-left">Total Stores</th>
                  <th className="px-4 py-2 text-left">Reasoning</th>
                  <th className="px-4 py-2 text-left">State Distribution</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(analysis.finalAssignments)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([groupName, assignment]) => (
                    <tr key={groupName} className="border-t hover:bg-gray-50">
                      <td className="px-4 py-2 font-medium">{groupName}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded text-sm ${
                          assignment.primaryRegion === 'Northeast' ? 'bg-blue-100 text-blue-800' :
                          assignment.primaryRegion === 'Midwest' ? 'bg-green-100 text-green-800' :
                          assignment.primaryRegion === 'South' ? 'bg-orange-100 text-orange-800' :
                          assignment.primaryRegion === 'West' ? 'bg-red-100 text-red-800' :
                          'bg-purple-100 text-purple-800'
                        }`}>
                          {assignment.primaryRegion}
                        </span>
                      </td>
                      <td className="px-4 py-2">{assignment.totalStores}</td>
                      <td className="px-4 py-2 text-sm">{assignment.reasoning}</td>
                      <td className="px-4 py-2 text-sm">
                        {Object.entries(assignment.stateDistribution)
                          .map(([state, count]) => `${state}: ${count}`)
                          .join(', ')}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {selectedView === 'methodology' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Analysis Methodology</h2>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Regional Definitions (US Census Regions)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(regions).map(([region, states]) => (
                <div key={region} className="bg-white p-3 rounded border">
                  <h4 className="font-semibold text-blue-600">{region}</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {states.join(', ')}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Assignment Rules</h3>
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li><strong>Single State:</strong> If all stores in a group are in the same state, assign to that state's region</li>
              <li><strong>Multi-State:</strong> Assign to the region containing the majority of stores</li>
              <li><strong>Tie-Breaking:</strong> In case of equal distribution, assign to alphabetically first region</li>
              <li><strong>Special Cases:</strong> Handle non-standard state codes (like SC11) appropriately</li>
              <li><strong>International:</strong> Canadian provinces (ON) assigned to "International" region</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Data Processing</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Parse the input data to extract group names, store names, and state codes</li>
              <li>Group all stores by their parent automotive group</li>
              <li>Count store distribution across states and regions for each group</li>
              <li>Apply assignment rules to determine primary region for each group</li>
              <li>Generate reasoning explanation for each assignment</li>
              <li>Compile summary statistics and visualizations</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomotiveGroupAnalyzer;