<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>

    <!-- Rasa Webchat CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/rasa-webchat@1.0.0/dist/rasa-webchat.min.css">
  </head>
  <body>
    <!-- React App Root -->
    <div id="root"></div>

    <!-- Rasa Webchat Container -->
    <div id="webchat"></div>

    Rasa Webchat JS (Ensure it's loaded before usage)
    <script src="https://cdn.jsdelivr.net/npm/rasa-webchat@1.0.0/dist/rasa-webchat.min.js"></script>

<script src='https://www.noupe.com/embed/0198df70b3a97374a8920036a5afd71c92c1.js'></script>

    <!-- React App Script (main.tsx or main.js) -->
    <script type="module" src="/src/main.tsx"></script>

  </body>
</html>
