import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';

const AutomotiveGroupAnalyzer = () => {
  const [data, setData] = useState([]);
  const [analysis, setAnalysis] = useState(null);
  const [selectedView, setSelectedView] = useState('summary');

  // US Census Regional Definitions
  const regions = {
    'Northeast': ['CT', 'ME', 'MA', 'NH', 'NJ', 'NY', 'PA', 'RI', 'VT'],
    'Midwest': ['IL', 'IN', 'IA', 'KS', 'MI', 'MN', 'MO', 'NE', 'ND', 'OH', 'SD', 'WI'],
    'South': ['AL', 'AR', 'DE', 'FL', 'GA', 'KY', 'LA', 'MD', 'MS', 'NC', 'OK', 'SC', 'TN', 'TX', 'VA', 'WV'],
    'West': ['AK', 'AZ', 'CA', 'CO', 'HI', 'ID', 'MT', 'NV', 'NM', 'OR', 'UT', 'WA', 'WY'],
    'International': ['ON'] // Canadian provinces
  };

  const rawData = `Alfa Romeo Maserati of St Petersburg|Maserati St Petersburg|FL        
All Star Automotive Group|All Star Ford|LA        
All Star Automotive Group|All Star Chevrolet|LA        
All Star Automotive Group|All Star Chevrolet North|LA        
All Star Automotive Group|All Star Dodge Chrysler Jeep Ram|LA        
All Star Automotive Group|All Star Kia|LA        
All Star Automotive Group|All Star Kia East|LA
All Star Automotive Group|All Star Hyundai|LA
All Star Automotive Group|All Star Nissan|LA
All Star Automotive Group|All Star Toyota|LA
All Star Automotive Group|All Star Volvo|LA
All Star Automotive Group|All Star Ford Lincoln|LA
Anderson Automotive Group (NC)|Fred Anderson Kia of Greenville|SC
Anderson Automotive Group (NC)|Fred Anderson Kia of Greer|SC
Anderson Automotive Group (NC)|Fred Anderson Toyota of Greer|SC
Anderson Automotive Group (NC)|Fred Anderson Honda|S
Anderson Automotive Group (NC)|Fred Anderson Nissan of Asheville|NC
Anderson Automotive Group (NC)|Fred Anderson Kia|NC
Anderson Automotive Group (NC)|Fred Anderson Toyota of Sanford|NC
Anderson Automotive Group (NC)|Fred Anderson Subaru|NC
Anderson Automotive Group (NC)|Fred Anderson Acura|SC
Anderson Automotive Group (NC)|Fred Anderson Toyota of Charleston|SC
Anderson Automotive Group (NC)|Fred Anderson Toyota of Raleigh|NC
Anderson Automotive Group (NC)|Fred Anderson Chevrolet/Cadillac|SC
Anderson Automotive Group (NC)|Fred Anderson Toyota of Asheville|NC
Anderson Automotive Group (NC)|Fred Anderson Hyundai of Greer|SC
Anderson Automotive Group (NC)|Genesis of Greer|SC
Anderson Automotive Group (NC)|Fred Anderson Nissan of Raleigh|NC
Apple Honda NY|Apple Honda NY|NY
Barnes Crossing Auto Group|Mazda of Jackson|MS
Barnes Crossing Auto Group|Barnes Crossing Chevrolet Buick GMC|MS
Barnes Crossing Auto Group|Barnes Crossing Ford|MS
Barnes Crossing Auto Group|Barnes Crossing Hyundai|MS
Barnes Crossing Auto Group|Barnes Crossing Kia|MS
Barnes Crossing Auto Group|Barnes Crossing Mitsubishi|MS
Barnes Crossing Auto Group|Barnes Crossing Volkswagen|MS
Bill Knight Auto Group|Bill Knight Lincoln|OK
Bill Knight Auto Group|Bill Knight Ford of Stillwater|OK
Billion Automotive|Sioux Falls Chrysler Jeep Dodge Ram Fiat|SD
Billion Automotive|Iowa City Chevrolet Buick GMC|IA
Billion Automotive|Sioux Falls Buick GMC|SD
Bob Bell Automotive Group|Bob Bell Chevrolet of Bel Air|MD
Bob Bell Automotive Group|Bob Bell Hyundai|MD
Bob Bell Automotive Group|Bob Bell Ford|MD
Bob Bell Automotive Group|Bob Bell Nissan - Kia|MD
Bob Bell Automotive Group|Bob Bell Chevrolet|MD
Burns Kull Automotive|Burns Honda|NJ
Carriage Automotive Group|Carriage Kia of Alpharetta|GA
Carriage Automotive Group|Carriage Kia of Woodstock|GA
Charlie Obaugh Automotive|Charlie Obaugh Chevrolet|VA
Charlies Motor Mall|Charlie's Motor Mall|ME
Chastang Ford|Chastang Ford|TX
ChevyLand|Chevyland|SC
Colley Ford|Colley Ford|CA
Copeland Auto Group|Copeland Chevrolet Hyannis|MA
Copeland Auto Group|Copeland Subaru Hyannis|MA
Criswell Auto Group|Criswell CDJR of Woodstock|VA
Criswell Auto Group|Criswell Chevrolet|MD
Criswell Auto Group|Criswell Chrysler Jeep Dodge Ram Fiat|MD
Criswell Auto Group|Criswell CDJR of Thurmont|MD
Critz Auto Group|Critz Buick GMC|GA
Critz Auto Group|Critz Auto Group Mercedes Sprinter Subscription|GA
Critz Auto Group|Critz BMW|GA
Dave Gill Chevrolet|Dave Gill Chevrolet|OH
DeYarman Automotive Group|DeYarman CDJR Ames|IA
DeYarman Automotive Group|DeYarman Ford Indianola|IA
DeYarman Automotive Group|DeYarman Chevrolet Buick GMC|IA
Don K Auto Group|Don K Chrysler Dodge Jeep Ram|MT
Don K Auto Group|Don K Chevrolet|MT
Don K Auto Group|Don K Subaru|MT
Dowling Ford|Dowling Ford|CT
Dublin Auto Group|Dublin Hyundai|CA
East Dallas Volkswagen|East Dallas Volkswagen|TX
Ed Kenley Ford|Ed Kenley Ford|UT
Faith's Auto Group|Faith's Toyota Ford|VT
Findlay Automotive Group|Findlay Volvo Cars Las Vegas|NV
Findlay Automotive Group|Findlay Subaru St George|UT
Findlay Automotive Group|Findlay Cadillac|NV
Findlay Automotive Group|Findlay Honda Henderson|NV
Findlay Automotive Group|Audi Reno Tahoe|NV
First Team Auto|First Team Kia Suffolk|VA
First Team Auto|First Team Subaru Suffolk|VA
First Team Auto|First Team Subaru Norfolk|VA
First Team Auto|First Team Honda|VA
First Team Auto|First Team Toyota|VA
First Team Auto|Hampton Chevrolet|VA
Fitzgerald Auto Mall|Fitzgerald Toy-Nis Chambersburg|PA
Ford of Dalton|Ford of Dalton|GA
Foss Motors|Foss Motors|CT
Fox Motors|Fox Acura Grand Rapids|MI
Fox Motors|Fox Nissan Grand Rapids|MI
Fred Beans Group|Fred Beans Ford of Exton|PA
Fred Beans Group|Fred Beans Ford of Washington|NJ
Fred Beans Group|Fred Beans Volkswagen Devon|PA
Fred Beans Group|Fred Beans Subaru|PA
Fuller Ford|Fuller Ford|OH
Ginn Auto Group|Ginn CJDR|GA
Ginn Auto Group|Ginn Motor Co. (Chev)|GA
Gold Rush Auto Group|Gold Rush Chevrolet|CA
Gold Rush Auto Group|Gold Rush Subaru|CA
Golden Circle Auto Group|Golden Circle Chevrolet|TN
Golden Circle Auto Group|Bolivar Ford|TN
Goss Auto Group|Goss Dodge Chrysler Ram Jeep|VT
Goss Auto Group|Goss Chevrolet|NY
Grainger Auto|Grainger Nissan of Anderson|SC
Granite Subaru|Granite Subaru|NH
Greater Lowell Buick GMC|Greater Lowell Buick GMC|MA
Haley Automotive Group|Haley Chrysler Dodge Jeep Ram|VA
Haley Automotive Group|Haley Buick GMC Truck|VA
Haley Automotive Group|Haley Buick GMC Airport|VA
Haley Automotive Group|Haley Toyota of Roanoke|VA
Haley Automotive Group|Haley Automall|VA
Haley Automotive Group|Haley Toyota Certified Pre-Owned Center|VA
Haley Automotive Group|Volvo Cars Richmond|VA
Haley Automotive Group|Haley Toyota of Richmond-Midlothian|VA
Haley Automotive Group|Haley Ford|VA
Haley Automotive Group|Haley Chevrolet|VA
Haley Automotive Group|Volvo Cars Midlothian|VA
Harnish Auto Family|Kia of Everett|WA
Hollingsworth Richards Ford|Hollingsworth Richards Ford|LA
Hunter Auto Group|Hunter Volvo|NC
Hunter Auto Group|Hunter Hyundai|NC
Hunter Auto Group|Hunter Subaru|NC
HW Kia of West County|HW Kia of West County|MO
Hyundai San Luis Obispo|Hyundai San Luis Obispo|CA
Jake Sweeney Automotive|BMW of Cincinnati North|OH
Jake Sweeney Automotive|Jake Sweeney Chevrolet|OH
Jake Sweeney Automotive|Jake Sweeney Kia|KY
Jeff Perry Buick GMC|Jeff Perry Buick GMC|IL
Karl Auto Group|Karl Chevrolet of Ankeny|IA
Karl Auto Group|Karl Chevrolet of Stuart|IA
Kenwood Dealer Group|Beechmont Subaru|OH
Kenwood Dealer Group|Subaru of Kings Automall|OH
Kenwood Dealer Group|Lebanon Ford|OH
Kenwood Dealer Group|Interstate Ford (OH)|OH
Kevin Whitaker Auto Group|Kevin Whitaker Chevrolet|SC
Key Cars Auto Group|Key Hyundai of Manchester|CT
Key Cars Auto Group|Key Chevrolet|CT
Key Cars Auto Group|Key Hyundai-Genesis of Milford|CT
Keystone AG OK|Carter County Hyundai|OK
Keystone AG OK|Carter County CDJR|OK
Keystone Auto Group|Keystone Chevrolet|OK
Kings Ford|Kings Ford|OH
Koons of Silver Spring|Koons Silver Spring Ford|MD
L & L Motor Co|L & L Ford - CDJR|UT
Lakeshore Automotive (LA)|Lakeshore CDJR (LA)|LA
Law Automotive Group|Springfield Mitsubishi|PA
Law Automotive Group|Volkswagen of Springfield|PA
Leskovar Mitsubishi|Leskovar Mitsubishi|WA
Lou Fusz Automotive Network|Lou Fusz Ford|MO
Lou Fusz Automotive Network|Lou Fusz Kia of Evansville|IN
Lou Fusz Automotive Network|Lou Fusz Kia Terre Haute|IN
Lou Fusz Automotive Network|Lou Fusz CDJR of Vincennes|IN
Lou Sobh Auto Group|Julio Jones Kia|AL
Loyal Test AG|Love Chevrolet|SC
Loyal Test AG|Love Chevrolet|SC
Loyal Test AG|Test Store New1|SC11
Loyal Test AG|Loyal Chevrolet|VA
Loyal Test AG|Love Chevrolet|SC
Loyal Test AG|Loyal Volvo|OK
Magic City Auto Group|Magic City Ford Lincoln|VA
Main Line Automotive Group|Audi Wynnewood|PA
Main Line Automotive Group|Audi West Chester|PA
Main Line Automotive Group|Audi Service Conshohocken|PA
Main Line Automotive Group|Porsche Conshohocken|PA
Mataga Buick GMC Cadillac|Mataga Buick GMC Cadillac|CA
Michael Comuzzi Auto Group|Superior Hyundai|ON
Michael Comuzzi Auto Group|St Laurent Jeep|ON
Michael Comuzzi Auto Group|Lakehead Motors|ON
Mile One Automotive|Mercedes-Benz of Owings Mills|MD
Mile One Automotive|Mercedes-Benz of Silver Spring|MD
Mile One Automotive|BMW of Fort Washington|PA
MileOne Automotive|BMW of Catonsville|MD
MileOne Automotive|BMW of Silver Spring|MD
Miller Brothers|Miller Brothers Chevrolet Cadillac|MD
Muller Auto Group|Muller Acura of Merrillville|IN
Myrtle Beach Chevrolet|Myrtle Beach Chevrolet|SC
Nationwide Motor Sales|Nationwide Infiniti of Timonium|MD
Nationwide Motor Sales|Nationwide Kia|MD
Nationwide Motor Sales|Nationwide Nissan|MD
Orange Buick GMC|Orange Buick GMC|FL
Ourisman Automotive (Main)|Toyota of Woodbridge|VA
Page Auto Group VA|Audi Richmond|VA
Page Auto Group VA|West Broad Honda|VA
Page Auto Group VA|Mechanicsville Honda|VA
Page Auto Group VA|West Broad Hyundai|VA
Page Auto Group VA|Mechanicsville Toyota|VA
Page Auto Group VA|West Broad Kia|VA
Passport Automotive Group|Passport BMW|MD
Passport Automotive Group|MINI of Alexandria|VA
Passport Automotive Group|Passport MINI of Montgomery County|MD
Passport Automotive Group|Passport BMW|MD
Passport Automotive Group|Passport MINI of Montgomery County|MD
Pat Milliken Ford|Pat Milliken Ford|MI
Peninsula Subaru|Peninsula Subaru|WA
Penn Toyota|Penn Toyota|NY
Piazza Auto Group|Mercedes-Benz of Wilmington|DE
Piazza Auto Group|Mike Piazza Honda|PA
Piazza Auto Group|Piazza Honda of Pottstown|PA
Piazza Auto Group|Piazza Mazda of West Chester|PA
Piazza Auto Group|Piazza Volkswagen of Langhorne|PA
Premier Automotive|Premier Hyundai of Moreno Valley|CA
Raabe Ford Lincoln|Raabe Ford Lincoln|OH
Ralph Sellers Auto Group|Ralph Sellers CDJR|LA
Ralph Sellers Auto Group|Ralph Sellers Chevrolet|LA
Ralph Sellers Auto Group|Ralph Sellers Hyundai|LA
Randy Marion Auto Group|Randy Marion Chevrolet - Statesville|NC
Redding Subaru|Redding Subaru|CA
RiverHead Motors|Riverhead GMC|NY
RiverHead Motors|Riverhead Ford|NY
Riverhead Toyota|Riverhead Toyota|NY
RK Chevrolet|RK Chevrolet|VA
Robert Brogden AG|Robert Brogden Olathe Buick GMC|KS
Ross Downing Auto|Ross Downing Buick GMC Cadillac|LA
Ross Downing Auto|Ross Downing Buick GMC Of Gonzales|LA
Ross Downing Auto|Ross Downing Chevrolet|LA
Ross Downing Auto|Ross Downing CDJR|LA
Safford Automotive Group|Safford Brown Honda Arlington|VA
Safford Automotive Group|Safford Brown CDJR Sterling|VA
Safford Automotive Group|Safford Brown Honda Glen Burnie|MD
Safford Automotive Group|Safford Kia of Fredericksburg|VA
Safford Automotive Group|Safford Brown Subaru Manassas|VA
Safford Automotive Group|Virginia Auto Outlet|VA
Safford Automotive Group|Safford Brown Kia Manassas|VA
Safford Automotive Group|Safford Brown Hyundai Manassas|VA
Safford Automotive Group|Safford Brown Mazda Alexandria|VA
Safford Automotive Group|Safford Brown Hyundai Leesburg|VA
Safford Automotive Group|Safford-Brown Hyundai Fairfax|VA
Safford Automotive Group|Safford Brown Mazda Chantilly|VA
Safford Automotive Group|Safford Brown Mazda Fairfax|VA
Safford Automotive Group|Safford Brown Nissan Sterling|VA
Safford Automotive Group|Safford Brown Toyota Glen Burnie|MD
Safford Automotive Group|Safford Brown VW Richmond|VA
Sam Leman Automotive Group|Sam Leman_Mercedes of Champaign|IL
Sam Leman Automotive Group|Sam Leman Chevrolet Cadillac|IL
Sam Leman Automotive Group|BMW of Bloomington & Sam Leman Mazda|IL
Sam Leman Automotive Group|Lemans Chevy City|IL
Sam Leman Automotive Group|Sam Leman CDJR Champaign|IL
Sam Leman Automotive Group|Sam Leman Chrysler Dodge Jeep Ram Bloomington|IL
Sam Leman Automotive Group|Sam Leman Ford|IL
Sam Leman Automotive Group|Sam Leman Chrysler Dodge Jeep Ram Peoria|IL
Sam Leman Automotive Group|Sam Leman Chrysler Dodge Jeep Ram Morton|IL
Sam Leman Automotive Group|Sam Leman Toyota Bloomington|IL
Sam Leman Automotive Group|BMW of Peoria|IL
Sam Pack Auto Group|Five Star Ford of North Richland Hills|TX
Sam Pack Auto Group|Five Star Chevrolet|TX
Sam Pack Auto Group|Five Star Ford of Dallas|TX
Sam Pack Auto Group|Five Star Ford Carrollton|TX
Sam Pack Auto Group|Five Star Subaru|TX
Sam Pack Auto Group|Five Star Ford of Lewisville|TX
Serra Automotive|Serra Lexus Lansing|MI
Serra Automotive|Serra Auto Campus|MI
Sheehy Auto Stores|Sheehy Subaru of Springfield|VA
Sheehy Auto Stores|Sheehy Volkswagen of Springfield|VA
Sheehy Auto Stores|Sheehy Nissan of Glen Burnie|MD
Sheehy Auto Stores|Sheehy Lexus of Richmond|VA
Stivers Auto Group|Hyundai-Genesis|SC
Sunrise Auto Group|Sunrise Toyota North|NY
Sunrise Auto Group|Sunrise Toyota|NY
Sunrise Auto Group|Sunrise Toyota North|NY
Sunrise Auto Group|Sunrise Toyota|NY
The Rydell Company|Lundes Peoria VW|AZ
Thurston Auto Corporations|Thurston Honda|CA
Towbin Auto Group|Towbin Kia|NV
Turner Automotive Corporation|Turner KIA|PA
United Chevrolet GMC|United Chevrolet GMC|IL
Vande Hey Brantmeier|Vande Hey Brantmeier Chevrolet Buick|WI
Vande Hey Brantmeier|Vande Hey Brantmeier CDJR|WI
Vann York Auto|Freedom Honda Sumter|SC
Vann York Auto|Vann York Nissan|NC
Vann York Auto|Vann York Honda|NC
Vann York Auto|Vann York Toyota|NC
Vann York Auto|Vann York Chevrolet|NC
Walterboro Ford|Walterboro Ford|SC
Whitten Brothers|Whitten Bros Chrysler Dodge Jeep of Richmond|VA
Whitten Brothers|Whitten Bros Mazda|VA
Whitten Brothers|Whitten Brothers Chrysler Dodge Jeep RAM of Richmond|VA
Wondries Auto Group|Wondries Toyota|CA
Young Automotive Group MI|Kia of Jackson|MI
Young Automotive Group MI|Kia of Lansing|MI
Young Automotive Group MI|Young Chevrolet GMC of Ionia|MI
Young Automotive Group MI|Young Chevrolet of St Johns|MI
Young Automotive Group MI|Young Chevrolet Cadillac|MI
Young Automotive Group MI|Young Buick GMC|MI`;

  const getStateRegion = (stateCode) => {
    // Handle special case for test data
    if (stateCode === 'SC11') return 'South';
    
    for (const [region, states] of Object.entries(regions)) {
      if (states.includes(stateCode)) {
        return region;
      }
    }
    return 'Unknown';
  };

  const parseData = () => {
    const parsed = [];
    const lines = rawData.trim().split('\n');
    
    lines.forEach(line => {
      const parts = line.split('|');
      if (parts.length === 3) {
        const [groupName, storeName, stateCode] = parts;
        parsed.push({
          groupName: groupName.trim(),
          storeName: storeName.trim(),
          stateCode: stateCode.trim()
        });
      }
    });
    
    return parsed;
  };

  const analyzeData = (parsedData) => {
    const groupAnalysis = {};
    
    // Group stores by automotive group
    parsedData.forEach(store => {
      if (!groupAnalysis[store.groupName]) {
        groupAnalysis[store.groupName] = {
          stores: [],
          stateDistribution: {},
          regionDistribution: {}
        };
      }
      
      groupAnalysis[store.groupName].stores.push(store);
      
      // Count states
      const state = store.stateCode;
      groupAnalysis[store.groupName].stateDistribution[state] = 
        (groupAnalysis[store.groupName].stateDistribution[state]|| 0) + 1;
      
      // Count regions
      const region = getStateRegion(state);
      groupAnalysis[store.groupName].regionDistribution[region] = 
        (groupAnalysis[store.groupName].regionDistribution[region]|| 0) + 1;
    });

    // Determine primary region for each group
    const finalAssignments = {};
    Object.entries(groupAnalysis).forEach(([groupName, data]) => {
      const regionCounts = data.regionDistribution;
      let primaryRegion = '';
      let maxCount = 0;

      // Find region with most stores
      Object.entries(regionCounts).forEach(([region, count]) => {
        if (count > maxCount|| (count === maxCount && region < primaryRegion)) {
          maxCount = count;
          primaryRegion = region;
        }
      });

      finalAssignments[groupName] = {
        primaryRegion,
        totalStores: data.stores.length,
        stateDistribution: data.stateDistribution,
        regionDistribution: data.regionDistribution,
        reasoning: generateReasoning(data, primaryRegion)
      };
    });

    return { groupAnalysis, finalAssignments };
  };

  const generateReasoning = (data, primaryRegion) => {
    const totalStores = data.stores.length;
    const regionCount = data.regionDistribution[primaryRegion];
    
    if (totalStores === 1) {
      return 'Single store location';
    } else if (regionCount === totalStores) {
      return `All ${totalStores} stores in ${primaryRegion}`;
    } else {
      return `${regionCount} of ${totalStores} stores in ${primaryRegion}`;
    }
  };

  useEffect(() => {
    const parsedData = parseData();
    setData(parsedData);
    const analysisResult = analyzeData(parsedData);
    setAnalysis(analysisResult);
  }, []);

  const getRegionSummary = () => {
    if (!analysis) return [];
    
    const regionCounts = {};
    Object.values(analysis.finalAssignments).forEach(assignment => {
      regionCounts[assignment.primaryRegion] = (regionCounts[assignment.primaryRegion]|| 0) + 1;
    });

    return Object.entries(regionCounts).map(([region, count]) => ({
      region,
      count
    }));
  };

  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#ff0000'];

  if (!analysis) {
    return <div className="p-6">Loading analysis...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">
        Automotive Group Regional Analysis
      </h1>

      <div className="mb-6">
        <div className="flex space-x-4 mb-4">
          <button
            onClick={() => setSelectedView('summary')}
            className={`px-4 py-2 rounded ${
              selectedView === 'summary' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Regional Summary
          </button>
          <button
            onClick={() => setSelectedView('detailed')}
            className={`px-4 py-2 rounded ${
              selectedView === 'detailed' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Detailed Analysis
          </button>
          <button
            onClick={() => setSelectedView('methodology')}
            className={`px-4 py-2 rounded ${
              selectedView === 'methodology' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Methodology
          </button>
        </div>
      </div>

      {selectedView === 'summary' && (
        <div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Groups by Region</h2>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={getRegionSummary()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="region" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Regional Distribution</h2>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={getRegionSummary()}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ region, count }) => `${region}: ${count}`}
                  >
                    {getRegionSummary().map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Quick Stats</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Object.keys(analysis.finalAssignments).length}
                </div>
                <div className="text-sm text-gray-600">Total Groups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.length}
                </div>
                <div className="text-sm text-gray-600">Total Stores</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {getRegionSummary().length}
                </div>
                <div className="text-sm text-gray-600">Regions Covered</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {new Set(data.map(d => d.stateCode)).size}
                </div>
                <div className="text-sm text-gray-600">States/Provinces</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedView === 'detailed' && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Detailed Group Assignments</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-300">
              <thead className="bg-gray-100">
                <tr>
                  <th className="px-4 py-2 text-left">Group Name</th>
                  <th className="px-4 py-2 text-left">Assigned Region</th>
                  <th className="px-4 py-2 text-left">Total Stores</th>
                  <th className="px-4 py-2 text-left">Reasoning</th>
                  <th className="px-4 py-2 text-left">State Distribution</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(analysis.finalAssignments)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([groupName, assignment]) => (
                    <tr key={groupName} className="border-t hover:bg-gray-50">
                      <td className="px-4 py-2 font-medium">{groupName}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded text-sm ${
                          assignment.primaryRegion === 'Northeast' ? 'bg-blue-100 text-blue-800' :
                          assignment.primaryRegion === 'Midwest' ? 'bg-green-100 text-green-800' :
                          assignment.primaryRegion === 'South' ? 'bg-orange-100 text-orange-800' :
                          assignment.primaryRegion === 'West' ? 'bg-red-100 text-red-800' :
                          'bg-purple-100 text-purple-800'
                        }`}>
                          {assignment.primaryRegion}
                        </span>
                      </td>
                      <td className="px-4 py-2">{assignment.totalStores}</td>
                      <td className="px-4 py-2 text-sm">{assignment.reasoning}</td>
                      <td className="px-4 py-2 text-sm">
                        {Object.entries(assignment.stateDistribution)
                          .map(([state, count]) => `${state}: ${count}`)
                          .join(', ')}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {selectedView === 'methodology' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Analysis Methodology</h2>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Regional Definitions (US Census Regions)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(regions).map(([region, states]) => (
                <div key={region} className="bg-white p-3 rounded border">
                  <h4 className="font-semibold text-blue-600">{region}</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {states.join(', ')}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Assignment Rules</h3>
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li><strong>Single State:</strong> If all stores in a group are in the same state, assign to that state's region</li>
              <li><strong>Multi-State:</strong> Assign to the region containing the majority of stores</li>
              <li><strong>Tie-Breaking:</strong> In case of equal distribution, assign to alphabetically first region</li>
              <li><strong>Special Cases:</strong> Handle non-standard state codes (like SC11) appropriately</li>
              <li><strong>International:</strong> Canadian provinces (ON) assigned to "International" region</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Data Processing</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Parse the input data to extract group names, store names, and state codes</li>
              <li>Group all stores by their parent automotive group</li>
              <li>Count store distribution across states and regions for each group</li>
              <li>Apply assignment rules to determine primary region for each group</li>
              <li>Generate reasoning explanation for each assignment</li>
              <li>Compile summary statistics and visualizations</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomotiveGroupAnalyzer;